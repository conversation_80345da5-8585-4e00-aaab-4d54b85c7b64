import React from 'react'
import { useToastStore } from '@/stores/toastStore'
import Toast from './Toast'

const ToastContainer: React.FC = () => {
  const { toasts } = useToastStore()

  if (toasts.length === 0) return null

  return (
    <div className="fixed top-6 right-6 z-50 space-y-3 pointer-events-none">
      {toasts.map((toast) => (
        <div key={toast.id} className="pointer-events-auto">
          <Toast toast={toast} />
        </div>
      ))}
    </div>
  )
}

export default ToastContainer
