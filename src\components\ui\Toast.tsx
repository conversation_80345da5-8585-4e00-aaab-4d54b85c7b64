import React, { useEffect } from 'react'
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react'
import { cn } from '@/utils/cn'
import { useToastStore, Toast as ToastType } from '@/stores/toastStore'

interface ToastProps {
  toast: ToastType
}

const Toast: React.FC<ToastProps> = ({ toast }) => {
  const { removeToast } = useToastStore()

  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-success-500" />
      case 'error':
        return <AlertCircle className="w-4 h-4 text-error-500" />
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-warning-500" />
      case 'info':
        return <Info className="w-4 h-4 text-brand-500" />
      default:
        return <Info className="w-4 h-4 text-brand-500" />
    }
  }

  const getToastTypeClass = () => {
    switch (toast.type) {
      case 'success':
        return 'toast-success'
      case 'error':
        return 'toast-error'
      case 'warning':
        return 'toast-warning'
      case 'info':
        return 'toast-info'
      default:
        return 'toast-info'
    }
  }

  return (
    <div
      className={cn(
        'toast flex items-start gap-3 max-w-sm w-full',
        getToastTypeClass()
      )}
    >
      {/* Icon */}
      <div className="flex-shrink-0 mt-0.5">
        {getIcon()}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="font-semibold text-text-primary text-sm leading-tight">
          {toast.title}
        </div>
        {toast.message && (
          <div className="text-text-tertiary text-sm mt-1 leading-relaxed">
            {toast.message}
          </div>
        )}

        {/* Action Button */}
        {toast.action && (
          <button
            onClick={toast.action.onClick}
            className={cn(
              'mt-3 px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-200',
              'bg-brand-500/10 text-brand-400 hover:bg-brand-500/20 hover:text-brand-300',
              'border border-brand-500/20 hover:border-brand-500/30'
            )}
          >
            {toast.action.label}
          </button>
        )}
      </div>

      {/* Close Button */}
      <button
        onClick={() => removeToast(toast.id)}
        className={cn(
          'flex-shrink-0 p-1 rounded-md transition-all duration-200',
          'text-text-quaternary hover:text-text-secondary hover:bg-dark-tertiary/50'
        )}
      >
        <X className="w-3.5 h-3.5" />
      </button>
    </div>
  )
}

export default Toast
