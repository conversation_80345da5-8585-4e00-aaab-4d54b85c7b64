@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    margin: 0;
    padding: 0;
    background-color: #0f0f0f; /* Dark theme primary background */
    color: #ffffff; /* Dark theme primary text */
    line-height: 1.5;
    font-weight: 400;
    letter-spacing: -0.02em; /* -2% kerning */
  }

  /* Custom scrollbar for dark theme */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #1a1a1a; /* Dark theme secondary */
  }

  ::-webkit-scrollbar-thumb {
    background: #374151; /* Dark theme border */
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #4b5563; /* Dark theme border hover */
  }

  @keyframes rotate-down {
    from { transform: rotate(0deg); }
    to { transform: rotate(180deg); }
  }

  @keyframes rotate-up {
    from { transform: rotate(180deg); }
    to { transform: rotate(0deg); }
  }

  /* Toast animations */
  @keyframes toast-slide-in {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes toast-slide-out {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
}

@layer components {
  /* Glass morphism utilities for dark theme */
  .glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Toast notification styles */
  .toast {
    @apply bg-dark-secondary border border-dark-quaternary/50 rounded-xl p-4;
    background: #202020;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 0.05),
                inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    animation: toast-slide-in 0.3s ease-out;
  }

  .toast-success {
    @apply border-l-4 border-l-success-500;
  }

  .toast-error {
    @apply border-l-4 border-l-error-500;
  }

  .toast-warning {
    @apply border-l-4 border-l-warning-500;
  }

  .toast-info {
    @apply border-l-4 border-l-brand-500;
  }

  /* iOS-style Button Styles */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-dark-primary;
    letter-spacing: -0.02em;
    border-radius: 12px;
    border: none;
    position: relative;
    overflow: hidden;
  }

  .btn-primary {
    @apply bg-gradient-button text-white font-medium;
    letter-spacing: -0.02em;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.25), 
                0 1px 0 rgba(255, 255, 255, 0.1) inset,
                0 -1px 0 rgba(0, 0, 0, 0.1) inset;
  }

  .btn-primary:hover {
    @apply bg-gradient-warm;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.35), 
                0 1px 0 rgba(255, 255, 255, 0.15) inset,
                0 -1px 0 rgba(0, 0, 0, 0.15) inset;
    transform: translateY(-1px);
  }

  .btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(99, 102, 241, 0.3), 
                0 1px 0 rgba(0, 0, 0, 0.1) inset,
                0 -1px 0 rgba(255, 255, 255, 0.1) inset;
  }

  .btn-secondary {
    @apply bg-dark-tertiary text-text-secondary font-medium;
    background: #282A29;
    letter-spacing: -0.02em;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15), 
                0 1px 0 rgba(255, 255, 255, 0.05) inset,
                0 -1px 0 rgba(0, 0, 0, 0.1) inset,
                0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .btn-secondary:hover {
    background: #303231;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.25), 
                0 1px 0 rgba(255, 255, 255, 0.08) inset,
                0 -1px 0 rgba(0, 0, 0, 0.15) inset;
    transform: translateY(-1px);
  }

  .btn-ghost {
    @apply text-text-secondary hover:text-text-primary hover:bg-dark-tertiary font-medium;
    letter-spacing: -0.02em;
    box-shadow: none;
  }

  .btn-ghost:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1), 
                0 1px 0 rgba(255, 255, 255, 0.05) inset;
  }

  /* iOS-style Card Styles */
  .card {
    @apply bg-dark-secondary p-6;
    background: #202020; /* Updated card color */
    border-radius: 16px;
    border: none;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 0.05),
                inset 0 -1px 0 rgba(0, 0, 0, 0.2);
  }

  .card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.06),
                inset 0 1px 0 rgba(255, 255, 255, 0.08),
                inset 0 -1px 0 rgba(0, 0, 0, 0.25);
  }

  .card-glass {
    @apply glass p-6;
    background: #20202080; /* semi-transparent glass variant */
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  /* Metric Card Styles */
  .metric-card {
    @apply bg-dark-secondary p-5;
    background: #202020;
    border-radius: 16px;
    border: none;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 0.05),
                inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
    min-height: 120px;
  }

  .metric-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.06),
                inset 0 1px 0 rgba(255, 255, 255, 0.08),
                inset 0 -1px 0 rgba(0, 0, 0, 0.25);
    transform: translateY(-1px);
  }

  /* Campaign & Activity card utilities */
  .campaign-card, .activity-card {
    background: #202020;
    border-radius: 12px;
    padding: 1.25rem; /* p-5 */
    box-shadow: 0 1px 4px rgba(0,0,0,0.25), 0 0 0 1px rgba(255,255,255,0.04), inset 0 1px 0 rgba(255,255,255,0.05), inset 0 -1px 0 rgba(0,0,0,0.2);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }
  .campaign-card:hover, .activity-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.3), 0 0 0 1px rgba(255,255,255,0.06), inset 0 1px 0 rgba(255,255,255,0.08), inset 0 -1px 0 rgba(0,0,0,0.25);
  }

  /* iOS-style Input styles */
  .input {
    @apply block w-full px-3 py-2 bg-dark-tertiary text-sm text-text-primary placeholder-text-quaternary focus:outline-none focus:ring-2 focus:ring-brand-500 font-normal;
    letter-spacing: -0.02em;
    border-radius: 12px;
    border: none;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 1px 0 rgba(0, 0, 0, 0.1),
                inset 0 -1px 0 rgba(255, 255, 255, 0.05);
  }

  .input:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1),
                0 0 0 1px rgba(99, 102, 241, 0.3),
                inset 0 1px 0 rgba(0, 0, 0, 0.1),
                inset 0 -1px 0 rgba(255, 255, 255, 0.05);
  }

  /* Search input styles */
  .search-input {
    @apply bg-dark-tertiary text-text-primary placeholder-text-quaternary focus:outline-none focus:ring-2 focus:ring-brand-500;
    @apply px-3 py-2 text-sm rounded-lg;
    border: none;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 1px 0 rgba(0, 0, 0, 0.1),
                inset 0 -1px 0 rgba(255, 255, 255, 0.05);
  }

  /* Style date input text color */
  input[type="date"] {
    color-scheme: dark;
  }

  input[type="date"]:not(:valid) {
    color: theme('colors.text.quaternary');
  }

  /* Lighten native date picker icon */
  input[type="date"]::-webkit-calendar-picker-indicator {
    filter: invert(70%);
    opacity: 0.7;
  }

  .search-input:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1),
                0 0 0 1px rgba(99, 102, 241, 0.3),
                inset 0 1px 0 rgba(0, 0, 0, 0.1),
                inset 0 -1px 0 rgba(255, 255, 255, 0.05);
  }

  /* Toggle Switch Styles */
  .toggle-switch {
    width: 56px;
    height: 28px;
    background: #2a2a2a;
    border-radius: 14px;
    position: relative;
    cursor: pointer;
    transition: background 0.2s ease;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.04),
                inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                0 1px 3px rgba(0,0,0,0.4);
  }

  .toggle-switch.active {
    background: #10b981;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2),
                inset 0 -1px 0 rgba(0, 0, 0, 0.2);
  }

  .toggle-switch::before {
    content: '';
    position: absolute;
    width: 24px;
    height: 24px;
    background: #ffffff;
    border-radius: 50%;
    top: 2px;
    left: 2px;
    transition: transform 0.2s ease;
    box-shadow: inset 0 1px 0 rgba(255,255,255,0.6), inset 0 -1px 0 rgba(0,0,0,0.25), 0 1px 3px rgba(0,0,0,0.4);
  }

  .toggle-switch.active::before {
    transform: translateX(28px);
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  .animate-rotate-down {
    animation: rotate-down 0.2s ease-in-out forwards;
  }

  .animate-rotate-up {
    animation: rotate-up 0.2s ease-in-out forwards;
  }
}

@layer utilities {
  /* Text gradient utilities */
  .text-gradient-primary {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-secondary {
    background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Shadow utilities - dark theme */
  .shadow-xs {
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  }

  .shadow-glow {
    box-shadow: 0 4px 14px 0 rgb(0 0 0 / 0.4), 0 0 0 1px rgb(255 255 255 / 0.05);
  }

  .shadow-glow-purple {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.15);
  }

  /* Font weight utilities for Inter with dark theme */
  .font-thin { font-weight: 100; letter-spacing: -0.02em; }
  .font-extralight { font-weight: 200; letter-spacing: -0.02em; }
  .font-light { font-weight: 300; letter-spacing: -0.02em; }
  .font-normal { font-weight: 400; letter-spacing: -0.02em; }
  .font-medium { font-weight: 500; letter-spacing: -0.02em; }
  .font-semibold { font-weight: 600; letter-spacing: -0.02em; }
  .font-bold { font-weight: 700; letter-spacing: -0.02em; }
  .font-extrabold { font-weight: 800; letter-spacing: -0.02em; }
  .font-black { font-weight: 900; letter-spacing: -0.02em; }

  /* Letter spacing utilities */
  .tracking-tight { letter-spacing: -0.02em; }
  .tracking-tighter { letter-spacing: -0.04em; }
  .tracking-normal { letter-spacing: 0em; }

  /* Safe area utilities for mobile */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Canvas layout utilities */
  .canvas-layout {
    background: #0A0A0A;
    min-height: 100vh;
  }

  .main-content-container {
    background: #181818;
    border-radius: 20px;
    margin: 20px;
    margin-left: 276px; /* sidebar width + margin */
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5),
                0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    overflow: hidden;
  }

  .main-content-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  }

  /* Sidebar active item skeumorphic pill */
  .sidebar-item-active {
    background: #202020;
    border-radius: 10px; /* slightly less rounded */
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.35),
                inset 0 1px 0 rgba(255, 255, 255, 0.05),
                inset 0 -1px 0 rgba(0, 0, 0, 0.25);
  }

  /* Purple vertical bar with bevel */
  .sidebar-vertical-bar {
    background: linear-gradient(180deg, #7c66ff 0%, #6366f1 100%);
    box-shadow: inset 1px 0 rgba(255, 255, 255, 0.25), inset -1px 0 rgba(0, 0, 0, 0.4);
    border-radius: 2px;
  }

  /* Brand selector button */
  .brand-selector-btn {
    background: #262626; /* slightly lighter for better contrast */
    border-radius: 10px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.35),
                inset 0 1px 0 rgba(255, 255, 255, 0.05),
                inset 0 -1px 0 rgba(0, 0, 0, 0.25);
    transition: all 0.2s ease;
  }
  .brand-selector-btn:hover {
    background: #2b2b2b;
  }

  /* Brand selector button - selected state */
  .brand-selector-selected {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    color: #ffffff;
    box-shadow: 0 0 0 1px rgba(255,255,255,0.08), 0 0 0 3px rgba(99,102,241,0.25);
  }

  /* View toggle container and active button */
  .view-toggle {
    @apply inline-flex items-center rounded-xl p-1;
    background: #181818; /* flat, same as main content */
    border: 1px solid #2e2e2e; /* subtle outline */
  }

  .view-toggle-btn-active {
    @apply rounded-lg text-brand-500;
    background: #202020;
    box-shadow: inset 0 1px 0 rgba(255,255,255,0.06), inset 0 -1px 0 rgba(0,0,0,0.4), 0 2px 4px rgba(0,0,0,0.6);
  }

  /* Beveled campaign icon for list view */
  .campaign-list-icon {
    @apply w-8 h-8 flex items-center justify-center rounded-lg;
    box-shadow: inset 0 1px 0px rgba(255,255,255,0.4), inset 0 -1px 0px rgba(0,0,0,0.4), 0 2px 4px rgba(0,0,0,0.5);
  }

  .btn-gradient {
    @apply text-white font-medium;
    background-image: theme('backgroundImage.gradient-button-primary');
    box-shadow: 
      /* 1. Inner bevel for the 3D effect */
      inset 0 1px 1px rgba(255, 255, 255, 0.25),
      inset 0 -1px 1px rgba(0, 0, 0, 0.15),
      
      /* 2. The "border" - a light outline */
      0 0 0 1px rgba(255, 255, 255, 0.1),
      
      /* 3. A dark shadow just on the bottom edge to make the border "fade" */
      0 1px 2px rgba(0, 0, 0, 0.2),

      /* 4. The soft drop shadow */
      0 4px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.2s ease-in-out;
  }

  .btn-gradient:hover {
    transform: translateY(-1px);
    filter: brightness(1.1);
    box-shadow: 
      inset 0 1px 1px rgba(255, 255, 255, 0.3),
      inset 0 -1px 1px rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(255, 255, 255, 0.15),
      0 1px 2px rgba(0, 0, 0, 0.2),
      0 6px 12px rgba(0, 0, 0, 0.35);
  }

  /* Dropdown panel dark */
  .dropdown-panel {
    background: #202020;
    border: 1px solid #374151;
    border-radius: 10px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.2);
  }

  /* Dropdown trigger button */
  .dropdown-trigger {
    @apply bg-dark-tertiary border border-dark-quaternary text-text-secondary rounded-lg px-4 py-2 inline-flex items-center gap-2 transition-colors;
  }

  .dropdown-trigger:hover {
    @apply border-text-tertiary text-text-primary;
  }

  .dropdown-trigger-active {
    @apply border-brand-500 text-brand-500 shadow-glow-purple;
  }

  /* Dropdown item active (selected) */
  .dropdown-item-active {
    background: #262626;
    box-shadow: inset 0 0 0 1px #6366f1;
    @apply text-text-primary;
  }

  /* Generic dark thin separator */
  .separator-line {
    height: 1px;
    background: #2e2e2e;
  }

  /* Small beveled icon button */
  .icon-button {
    @apply w-10 h-10 flex items-center justify-center rounded-lg;
    box-shadow: inset 0 1px 0 rgba(255,255,255,0.05), inset 0 -1px 0 rgba(0,0,0,0.3), 0 1px 3px rgba(0,0,0,0.4);
  }
  .dropdown-item {
    @apply w-full text-left px-3 py-2 rounded-md text-sm text-text-secondary transition-colors;
  }
  .dropdown-item:hover {
    background: #262626;
    @apply text-text-primary;
  }
  /* Radio button indicator */
  .radio-indicator {
    @apply w-5 h-5 rounded-full border border-border flex items-center justify-center transition-colors relative;
  }
  .radio-indicator-selected {
    @apply border-brand-500 shadow-glow-purple;
  }
  .radio-indicator-selected::after {
    content: '';
    position: absolute;
    width: 9px; /* roughly w-2.5 */
    height: 9px;
    background: theme('colors.brand.500');
    border-radius: 9999px;
  }

  /* React DatePicker Dark Theme Styles */
  .react-datepicker-wrapper {
    width: 100%;
  }

  .react-datepicker {
    background-color: #1a1a1a !important; /* dark-secondary */
    border: 1px solid #3a3a3a !important; /* dark-quaternary */
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4) !important;
    font-family: 'Inter', sans-serif !important;
  }

  .react-datepicker__header {
    background-color: #2a2a2a !important; /* dark-tertiary */
    border-bottom: 1px solid #3a3a3a !important; /* dark-quaternary */
    border-top-left-radius: 12px !important;
    border-top-right-radius: 12px !important;
  }

  .react-datepicker__current-month,
  .react-datepicker-time__header,
  .react-datepicker-year-header {
    color: #ffffff !important; /* text-primary */
    font-weight: 600 !important;
  }

  .react-datepicker__day-name,
  .react-datepicker__day,
  .react-datepicker__time-name {
    color: #d1d5db !important; /* text-secondary */
  }

  .react-datepicker__day:hover {
    background-color: #3a3a3a !important; /* dark-quaternary */
    border-radius: 6px !important;
  }

  .react-datepicker__day--selected,
  .react-datepicker__day--in-selecting-range,
  .react-datepicker__day--in-range {
    background-color: #6366f1 !important; /* brand-500 */
    color: white !important;
    border-radius: 6px !important;
  }

  .react-datepicker__day--keyboard-selected {
    background-color: #4f46e5 !important; /* brand-600 */
    color: white !important;
    border-radius: 6px !important;
  }

  .react-datepicker__day--today {
    background-color: #2a2a2a !important; /* dark-tertiary */
    color: #6366f1 !important; /* brand-500 */
    font-weight: 600 !important;
    border-radius: 6px !important;
  }

  .react-datepicker__day--disabled {
    color: #6b7280 !important; /* text-tertiary */
  }

  .react-datepicker__navigation {
    top: 13px !important;
  }

  .react-datepicker__navigation--previous {
    border-right-color: #d1d5db !important; /* text-secondary */
  }

  .react-datepicker__navigation--next {
    border-left-color: #d1d5db !important; /* text-secondary */
  }

  .react-datepicker__time-container {
    border-left: 1px solid #3a3a3a !important; /* dark-quaternary */
  }

  .react-datepicker__time {
    background-color: #1a1a1a !important; /* dark-secondary */
  }

  .react-datepicker__time-box {
    width: 85px !important;
  }

  .react-datepicker__time-list-item {
    color: #d1d5db !important; /* text-secondary */
    padding: 8px 10px !important;
  }

  .react-datepicker__time-list-item:hover {
    background-color: #3a3a3a !important; /* dark-quaternary */
  }

  .react-datepicker__time-list-item--selected {
    background-color: #6366f1 !important; /* brand-500 */
    color: white !important;
    font-weight: 600 !important;
  }

  .react-datepicker__input-container input {
    background-color: #2a2a2a !important; /* dark-tertiary */
    border: 1px solid #3a3a3a !important; /* dark-quaternary */
    color: #ffffff !important; /* text-primary */
  }
}