import React, { useState, useEffect } from 'react'
import {
  Sparkles,
  Plus,
  Megaphone,
  FileText,
  TrendingUp,
  TrendingDown,
  Calendar,
  MessageCircle,
  CheckCircle2,
  Edit,
} from 'lucide-react'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { Status } from '@/components/ui'
import type { CampaignStatus } from '@/components/ui'
import { useNavigate } from 'react-router-dom'
import QuickPostWizard from '@/components/modals/QuickPostWizard'
import AddBrandModal from '@/components/modals/AddBrandModal'
import { useBrandStore } from '@/stores/brandStore'
import { useAuthStore } from '@/stores/authStore'
import { useCampaignStore } from '@/stores/campaignStore'

interface StatCardProps {
  title: string
  value: string
  change: string
  changeType: 'positive' | 'negative'
  trendSubtitle: string
}

const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  change, 
  changeType, 
  trendSubtitle,
}) => {
  const trendConfig = {
    positive: { icon: TrendingUp, color: 'text-success-500' },
    negative: { icon: TrendingDown, color: 'text-error-500' },
  }

  const { icon: TrendIcon, color } = trendConfig[changeType];

  return (
    <div className="metric-card">
      <div className="flex flex-col justify-between h-full">
        <div>
          <div className="flex items-center justify-between">
            <p className="text-xs font-medium text-text-tertiary uppercase tracking-wider">{title}</p>
            <TrendIcon className={`w-6 h-6 ${color}`} />
          </div>
          <p className="text-3xl font-light text-text-primary mt-2 mb-1 leading-none">{value}</p>
        </div>
        <div className="flex items-center gap-2 mt-3">
          <p className={`text-sm font-medium ${color}`}>{change}</p>
          <p className="text-xs text-text-tertiary">{trendSubtitle}</p>
        </div>
      </div>
    </div>
  )
}

interface CampaignItemProps {
  id: string;
  name: string;
  status: CampaignStatus;
  progress: number;
  dueDate: string;
  contentCount: number;
}

const CampaignItem: React.FC<CampaignItemProps> = ({
  id,
  name,
  status,
  progress,
  dueDate,
  contentCount,
}) => {
  const navigate = useNavigate();

  return (
    <div 
      onClick={() => navigate(`/campaigns/${id}`)}
      className="campaign-card cursor-pointer group"
      style={{
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1), inset 0 -1px 0 rgba(0, 0, 0, 0.2)'
      }}
    >
      <div className="flex items-start justify-between mb-3">
        <h3 className="font-medium text-text-primary text-sm">{name}</h3>
        <Status status={status} type="campaign" />
      </div>
      <div className="space-y-3">
        <div>
          <div className="flex justify-between text-xs text-text-tertiary mb-1.5">
            <span className="font-medium">Progress</span>
            <span className="font-medium text-text-secondary">{progress}%</span>
          </div>
          <div className="w-full bg-dark-quaternary rounded-full h-1.5">
            <div
              className="bg-gradient-button h-1.5 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
        <div className="flex items-center justify-between text-xs text-text-tertiary pt-2 border-t border-gray-600">
          <div className="flex items-center gap-1.5">
            <Calendar className="w-3.5 h-3.5" />
            <span>Due {dueDate}</span>
          </div>
          <div className="flex items-center gap-1.5">
            <FileText className="w-3.5 h-3.5" />
            <span>{contentCount} pieces</span>
          </div>
        </div>
      </div>
    </div>
  )
};

interface RecentActivityItem {
  id: string
  type: 'review' | 'request' | 'approval' | 'publish'
  title: string
  description: string
  time: string
  user: string
}

const recentActivities: RecentActivityItem[] = [
  {
    id: '1',
    type: 'review',
    title: 'Review submitted',
    description: 'Marcus Rodriguez reviewed "Summer Campaign 2024"',
    time: '45 minutes ago',
    user: 'Marcus Rodriguez',
  },
  {
    id: '2',
    type: 'request',
    title: 'Changes requested',
    description: 'Jane Doe requested changes for "Q3 Social Media Blitz"',
    time: '2 hours ago',
    user: 'Jane Doe',
  },
  {
    id: '3',
    type: 'approval',
    title: 'Content approved',
    description: '"New Product Teaser" has been approved and is ready for scheduling',
    time: '6 hours ago',
    user: 'John Smith',
  },
  {
    id: '4',
    type: 'publish',
    title: 'Content published',
    description: 'Instagram story "Weekly Highlights" went live',
    time: '1 day ago',
    user: 'Automated',
  }
]

const ActivityItem: React.FC<{ activity: RecentActivityItem }> = ({ activity }) => {
  const typeConfig = {
    review: { icon: MessageCircle, color: 'text-info-500' },
    request: { icon: Edit, color: 'text-warning-500' },
    approval: { icon: CheckCircle2, color: 'text-success-500' },
    publish: { icon: Megaphone, color: 'text-brand-500' }
  }

  const { icon: Icon, color } = typeConfig[activity.type as keyof typeof typeConfig]

  return (
    <div className="activity-card flex items-start gap-3 cursor-pointer">
      <Icon className={`w-4 h-4 flex-shrink-0 mt-0.5 ${color}`} />
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-text-primary">{activity.title}</p>
        <p className="text-xs text-text-secondary mt-1">{activity.description}</p>
        <p className="text-xs text-text-tertiary mt-1">{activity.time}</p>
      </div>
    </div>
  )
}

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { brand, activeBrand, isInitialized, fetchBrand } = useBrandStore();
  const { campaigns, dashboardStats, fetchCampaigns, fetchDashboardStats } = useCampaignStore();
  const [isCreatingQuickPost, setIsCreatingQuickPost] = useState(false);
  const [showBrandModal, setShowBrandModal] = useState(false);
  const [isMandatoryBrandCreation, setIsMandatoryBrandCreation] = useState(false);
  
  const currentHour = new Date().getHours()
  const greeting = currentHour < 12 ? 'Good morning' : currentHour < 18 ? 'Good afternoon' : 'Good evening'

  // Check brand status and fetch data on component mount
  useEffect(() => {
    const initializeDashboard = async () => {
      if (user) {
        await fetchBrand();
      }
    };

    initializeDashboard();
  }, [user, fetchBrand]);

  // Handle brand modal display based on brand state
  useEffect(() => {
    // Wait for brand store to be initialized before proceeding
    if (!isInitialized) {
      console.log('📊 Dashboard: Waiting for brand store initialization...');
      return;
    }

    if (user && brand === null) {
      // No brand exists, force brand creation
      setShowBrandModal(true);
      setIsMandatoryBrandCreation(true);
    } else if (user && (brand || activeBrand)) {
      // Brand exists, fetch campaigns and dashboard stats
      // Use the improved fetchCampaigns with brandId parameter
      if (activeBrand) {
        console.log('📊 Dashboard: Fetching data for active brand:', activeBrand.name);
        fetchCampaigns(activeBrand._id);
        fetchDashboardStats(activeBrand._id);
      } else {
        console.log('📊 Dashboard: Fetching data for all brands');
        fetchCampaigns();
        fetchDashboardStats();
      }
    }
  }, [user, brand, activeBrand, isInitialized, fetchCampaigns, fetchDashboardStats]);

  const handleCreateQuickPost = () => {
    setIsCreatingQuickPost(true);
  };

  const handleQuickPostBack = () => {
    setIsCreatingQuickPost(false);
  };

  const handleQuickPostGenerate = (postData: any, quickPostId?: string) => {
    console.log('Generating post with data:', postData);
    console.log('Quick post ID:', quickPostId);
    setIsCreatingQuickPost(false);

    // Navigate to the generated post view page
    if (quickPostId) {
      navigate(`/quick-posts/${quickPostId}`);
    }
  };

  const handleBrandModalClose = () => {
    // Only allow closing if not mandatory or if brand was created
    if (!isMandatoryBrandCreation || brand) {
      setShowBrandModal(false);
      setIsMandatoryBrandCreation(false);
    }
  };

  // If creating quick post, show wizard
  if (isCreatingQuickPost) {
    return (
      <QuickPostWizard
        onBack={handleQuickPostBack}
        onNext={handleQuickPostGenerate}
      />
    );
  }

  return (
    <>
      <div className="space-y-6">
        {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-text-primary">
            {greeting}, {user?.firstName || user?.fullName || 'User'}
          </h1>
          <p className="text-sm text-text-tertiary mt-1">
            Here's what's happening with your marketing today
          </p>
        </div>
        <Button
          variant="gradient"
          size="sm"
          icon={Sparkles}
          className="btn-primary"
          onClick={handleCreateQuickPost}
        >
          Quick Post
        </Button>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Total Campaigns"
          value={dashboardStats?.totalCampaigns?.toString() || "0"}
          change={dashboardStats?.campaignsGrowth || "+0%"}
          changeType="positive"
          trendSubtitle="this month"
        />
        <StatCard
          title="Total Posts"
          value={dashboardStats?.totalPosts?.toString() || "0"}
          change={dashboardStats?.postsGrowth || "+0%"}
          changeType="positive"
          trendSubtitle="this week"
        />
        <StatCard
          title="Engagement Rate"
          value={dashboardStats?.engagementRate || "0%"}
          change={dashboardStats?.engagementGrowth || "+0%"}
          changeType="positive"
          trendSubtitle="vs last month"
        />
        <StatCard
          title="Total Reach"
          value={dashboardStats?.totalReach || "0"}
          change={dashboardStats?.reachGrowth || "+0k"}
          changeType="positive"
          trendSubtitle="this month"
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-5 gap-6">
        {/* Recent Campaigns Card */}
        <Card className="xl:col-span-3 card">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-semibold text-text-primary">Recent Campaigns</CardTitle>
              <Button 
                variant="secondary"
                icon={Plus} 
                size="sm"
                className="btn-secondary"
                onClick={() => navigate('/campaigns')}
              >
                Create New
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {campaigns.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-5">
                {campaigns.slice(0, 4).map(campaign => (
                  <CampaignItem
                    key={campaign._id}
                    id={campaign._id}
                    name={campaign.name}
                    status={campaign.status as CampaignStatus}
                    progress={campaign.progress}
                    dueDate={campaign.dueDate}
                    contentCount={campaign.contentCount}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-text-tertiary mb-4">No campaigns yet</p>
                <Button 
                  variant="gradient"
                  icon={Plus}
                  size="sm"
                  onClick={() => navigate('/campaigns')}
                >
                  Create Your First Campaign
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
        
        {/* Recent Activity Card */}
        <Card className="xl:col-span-2 card">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-text-primary">Recent Activity</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              {recentActivities.map(activity => (
                <ActivityItem key={activity.id} activity={activity} />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>

    {/* Brand Creation Modal */}
    <AddBrandModal
      isOpen={showBrandModal}
      onClose={handleBrandModalClose}
    />
    </>
  )
}

export default Dashboard 