import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { api } from './authStore';

// Types for brand data (matching backend schema)
interface BrandInformation {
  name: string;
  about?: string;
  story?: string;
  location?: string;
  targetGeo?: string;
  website?: string;
  size: 'small' | 'medium' | 'large';
  type: 'startup' | 'enterprise' | 'government' | 'non-profit';
  industry: string;
}

interface SocialMedia {
  platform: string;
  url: string;
}

interface ContactInfo {
  phone: string[];
  email: string[];
}

interface TeamMember {
  name: string;
  role: string;
}

interface BrandIdentity {
  brandVoice: string[];
  coreMessagingPillars?: string;
  primaryKeywords?: string;
  negativeKeywords?: string;
}

interface Product {
  name: string;
  category: string;
  description: string;
  features?: string;
  customerBenefits?: string;
  uniqueSellingPropositions?: string;
  targetUseCases?: string;
  pricing?: string;
}

interface ICP {
  profileName: string;
  demographics?: string;
  psychographics?: string;
  goalsAndMotivations?: string;
  painPointsAndChallenges?: string;
  purchaseBehavior?: string;
  decisionMakerRole: string[];
  levelOfAwareness: string[];
  preferredContentChannels?: string;
}

interface Competitor {
  name: string;
  website?: string;
  targetAudience?: string;
  keyProducts?: string;
  strengths?: string;
  weaknesses?: string;
  differentiators?: string;
}

interface Document {
  _id?: string;
  originalName: string;
  filename: string;
  path: string;
  size: number;
  mimetype: string;
  uploadDate: string;
  namespace?: string;
  vectorProcessed?: boolean;
}

interface Reviewer {
  _id?: string;
  userId?: string;
  name: string;
  email: string;
  role?: string;
  type: 'internal' | 'external';
  addedDate: string;
}

interface Brand {
  _id: string;
  userId: string;
  name: string;
  about?: string;
  story?: string;
  location?: string;
  targetGeo?: string;
  website?: string;
  size: 'small' | 'medium' | 'large';
  type: 'startup' | 'enterprise' | 'government' | 'non-profit';
  industry: string;
  socialMedia: SocialMedia[];
  contactInfo: ContactInfo;
  keyTeamMembers: TeamMember[];
  brandIdentity: BrandIdentity;
  products: Product[];
  icps: ICP[];
  competitors: Competitor[];
  documents?: Document[];
  reviewers?: Reviewer[];
  isAIGenerated: boolean;
  createdAt: string;
  updatedAt: string;
}

interface AIAnalysisRequest {
  companyName: string;
  websiteUrl: string;
}

interface AIAnalysisResponse {
  brandInformation: BrandInformation;
  brandIdentity: BrandIdentity;
  products: Product[];
  icps: ICP[];
  competitors: Competitor[];
}

interface BrandState {
  // State
  brand: Brand | null;
  brands: Brand[];
  activeBrand: Brand | null; // Currently selected/active brand for workspace
  isLoading: boolean;
  error: string | null;
  aiAnalysisLoading: boolean;
  aiAnalysisData: AIAnalysisResponse | null;
  isInitialized: boolean; // Track if brand store has been properly initialized

  // Actions
  fetchBrand: () => Promise<void>;
  fetchAllBrands: () => Promise<Brand[]>;
  createBrand: (brandData: Partial<Brand>) => Promise<Brand>;
  updateBrand: (brandData: Partial<Brand>) => Promise<void>;
  analyzeBrandWithAI: (request: AIAnalysisRequest) => Promise<AIAnalysisResponse>;
  clearError: () => void;
  clearAIData: () => void;
  setBrand: (brand: Brand | null) => void;
  setActiveBrand: (brand: Brand | null) => void; // Set the active workspace brand
  switchBrand: (brand: Brand) => Promise<void>; // Switch to a different brand and refresh data
}

export const useBrandStore = create<BrandState>()(
  persist(
    (set, get) => ({
      // Initial state
      brand: null,
      brands: [],
      activeBrand: null, // Currently selected brand for workspace
      isLoading: false,
      error: null,
      aiAnalysisLoading: false,
      aiAnalysisData: null,
      isInitialized: false,

  // Fetch current user's brand
  fetchBrand: async () => {
    set({ isLoading: true, error: null });

    try {
      console.log('🔄 fetchBrand called');
      const response = await api.get('/brands/me');
      const { brand } = response.data.data;

      console.log('✅ Brand API response:', brand);

      set({
        brand: brand || null,
        isLoading: false,
        error: null
      });
    } catch (error: any) {
      console.error('❌ Brand API failed:', error.message);
      if (error.response?.status === 404) {
        // No brand found - this is expected for new users
        set({
          brand: null,
          isLoading: false,
          error: null
        });
      } else {
        const errorMessage = error.response?.data?.error?.message || 'Failed to fetch brand';
        set({
          brand: null,
          isLoading: false,
          error: errorMessage
        });
      }
    }
  },

  // Fetch all brands for current user
  fetchAllBrands: async () => {
    set({ isLoading: true, error: null });
    
    try {
      // Get user from auth store - we need to import it dynamically to avoid circular dependency
      const authStore = await import('./authStore');
      const { user } = authStore.useAuthStore.getState();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      const response = await api.get(`/brands/user/${user.id}`);
      const { brands } = response.data.data;
      const brandList = brands || [];

      set({
        brands: brandList,
        isLoading: false,
        error: null,
        isInitialized: true
      });

      // Handle active brand selection
      const { activeBrand } = get();

      if (brandList.length > 0) {
        // Check if stored active brand still exists in the brand list
        const storedActiveBrand = activeBrand;
        const isStoredBrandValid = storedActiveBrand && brandList.some(b => b._id === storedActiveBrand._id);

        if (isStoredBrandValid) {
          // Update the stored brand with fresh data
          const freshActiveBrand = brandList.find(b => b._id === storedActiveBrand._id);
          console.log('🔄 Brand store: Updating active brand with fresh data:', freshActiveBrand?.name);
          set({ activeBrand: freshActiveBrand });
        } else {
          // Set first brand as active if no valid stored brand
          console.log('🔄 Brand store: Setting first brand as active:', brandList[0].name);
          set({ activeBrand: brandList[0] });
        }
      } else {
        // No brands available, clear active brand
        console.log('🔄 Brand store: No brands available, clearing active brand');
        set({ activeBrand: null });
      }

      return brandList;
    } catch (error: any) {
      if (error.response?.status === 404) {
        // No brands found - this is expected for new users
        set({
          brands: [],
          isLoading: false,
          error: null
        });
        return [];
      } else {
        const errorMessage = error.response?.data?.error?.message || 'Failed to fetch brands';
        set({
          brands: [],
          isLoading: false,
          error: errorMessage
        });
        throw new Error(errorMessage);
      }
    }
  },

  // Create new brand
  createBrand: async (brandData: Partial<Brand>) => {
    set({ isLoading: true, error: null });

    try {
      const response = await api.post('/brands', brandData);
      const { brand } = response.data.data;

      set({
        brand,
        isLoading: false,
        error: null
      });

      console.log('✅ Brand created successfully');
      return brand; // Return the created brand
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || 'Failed to create brand';
      set({
        isLoading: false,
        error: errorMessage
      });
      throw new Error(errorMessage);
    }
  },

  // Update existing brand
  updateBrand: async (brandData: Partial<Brand>) => {
    const { brand } = get();
    if (!brand) {
      throw new Error('No brand to update');
    }
    
    set({ isLoading: true, error: null });
    
    try {
      const response = await api.put(`/brands/${brand._id}`, brandData);
      const { brand: updatedBrand } = response.data.data;
      
      set({
        brand: updatedBrand,
        isLoading: false,
        error: null
      });
      
      console.log('✅ Brand updated successfully');
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || 'Failed to update brand';
      set({
        isLoading: false,
        error: errorMessage
      });
      throw new Error(errorMessage);
    }
  },

  // Analyze brand with AI
  analyzeBrandWithAI: async (request: AIAnalysisRequest): Promise<AIAnalysisResponse> => {
    set({ aiAnalysisLoading: true, error: null });
    
    try {
      const response = await api.post('/ai/analyze-company', request);
      const aiData = response.data.data;
      
      set({
        aiAnalysisData: aiData,
        aiAnalysisLoading: false,
        error: null
      });
      
      console.log('✅ AI analysis completed successfully');
      return aiData;
    } catch (error: any) {
      console.error('❌ AI analysis error in store:', error);
      
      // Check if it's a network/timeout error
      if (error.code === 'ECONNABORTED' || 
          error.message?.includes('timeout') || 
          error.message?.includes('Network Error') ||
          !error.response) {
        console.log('🔄 Network/timeout error - keeping loading state');
        // For network/timeout errors, don't set error state
        // Keep loading state so user doesn't see premature error
        set({
          aiAnalysisLoading: true, // Keep loading
          error: null // Don't show error
        });
      } else {
        // Only set error for actual API errors
        const errorMessage = error.response?.data?.error?.message || 'AI analysis failed';
        set({
          aiAnalysisLoading: false,
          error: errorMessage
        });
      }
      
      throw error; // Still throw the error for the component to handle
    }
  },

  // Clear error
  clearError: () => {
    set({ error: null });
  },

  // Clear AI analysis data
  clearAIData: () => {
    set({ aiAnalysisData: null });
  },

  // Set brand directly
  setBrand: (brand: Brand | null) => {
    set({ brand });
  },

  // Set active brand for workspace
  setActiveBrand: (brand: Brand | null) => {
    set({ activeBrand: brand });
  },

  // Switch to a different brand and refresh related data
  switchBrand: async (brand: Brand) => {
    console.log(`🔄 Switching to brand: ${brand.name} (${brand._id})`);

    // Set the new active brand immediately
    set({ activeBrand: brand });

    // Clear and refresh data for the new brand
    try {
      const campaignStore = await import('./campaignStore');
      const { clearCampaigns, fetchCampaigns, fetchDashboardStatsByBrand } = campaignStore.useCampaignStore.getState();

      // Clear old campaign data first to prevent showing stale data
      clearCampaigns();
      console.log('🧹 Cleared old campaign data');

      // Fetch campaigns for the selected brand using the improved fetchCampaigns
      await fetchCampaigns(brand._id);
      console.log(`📊 Fetched campaigns for brand: ${brand.name}`);

      // Fetch brand-specific dashboard stats
      await fetchDashboardStatsByBrand(brand._id);
      console.log(`📈 Fetched dashboard stats for brand: ${brand.name}`);

      console.log(`✅ Successfully switched to brand: ${brand.name}`);
      console.log(`📝 Quick posts will be automatically refreshed by components that depend on activeBrand`);
    } catch (error) {
      console.error('❌ Error refreshing data after brand switch:', error);
    }
  }
    }),
    {
      name: 'brand-storage',
      partialize: (state) => ({
        activeBrand: state.activeBrand, // Only persist the active brand
      })
    }
  )
);

// Export types for use in components
export type {
  Brand,
  BrandInformation,
  Product,
  ICP,
  Competitor,
  AIAnalysisRequest,
  AIAnalysisResponse
};