import React, { useState, useEffect } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { cn } from '@/utils/cn'
import {
  ChevronDown,
  Building2,
  Plus,
} from 'lucide-react'
import { Icon } from '@iconify/react'
import { useBrandStore } from '@/stores/brandStore'
import { useAuthStore } from '@/stores/authStore'
import { useCampaignStore } from '@/stores/campaignStore'

interface SidebarProps {
  className?: string
  onAddBrand: () => void
}

// Import Brand type from store
import type { Brand } from '@/stores/brandStore'

// Icon Components using Streamline Solid Icons with dark theme colors
const OverviewIcon: React.FC<{ isActive?: boolean; className?: string }> = ({ isActive, className }) => {
  return (
    <Icon 
      icon="streamline:dashboard-3"
      className={cn(
        className, 
        isActive ? "text-brand-500" : "text-text-quaternary"
      )} 
    />
  )
}



const CampaignsIcon: React.FC<{ isActive?: boolean; className?: string }> = ({ isActive, className }) => {
  return (
    <Icon 
      icon="streamline:megaphone-2"
      className={cn(
        className, 
        isActive ? "text-brand-500" : "text-text-quaternary"
      )} 
    />
  )
}

const AnalyticsIcon: React.FC<{ isActive?: boolean; className?: string }> = ({ isActive, className }) => {
  return (
    <Icon 
      icon="streamline:graph-arrow-increase"
      className={cn(
        className, 
        isActive ? "text-brand-500" : "text-text-quaternary"
      )} 
    />
  )
}

const CalendarIcon: React.FC<{ isActive?: boolean; className?: string }> = ({ isActive, className }) => {
  return (
    <Icon 
      icon="streamline:blank-calendar"
      className={cn(
        className, 
        isActive ? "text-brand-500" : "text-text-quaternary"
      )} 
    />
  )
}

const BrandProfileIcon: React.FC<{ isActive?: boolean; className?: string }> = ({ isActive, className }) => {
  return (
    <Icon 
      icon="streamline:diamond-2"
      className={cn(
        className, 
        isActive ? "text-brand-500" : "text-text-quaternary"
      )} 
    />
  )
}

// Bottom Section Icons using Streamline Solid
const InboxIcon: React.FC<{ className?: string }> = ({ className }) => (
  <Icon icon="streamline:archive-box" className={className} />
)

const QuestionCircleIcon: React.FC<{ className?: string }> = ({ className }) => (
  <Icon icon="streamline:chat-bubble-square-question" className={className} />
)

const SettingsIcon: React.FC<{ className?: string }> = ({ className }) => (
  <Icon icon="streamline:cog" className={className} />
)

interface NavItem {
  id: string
  label: string
  icon: React.ElementType
  href: string
  badge?: string
}

const LumynLogo: React.FC = () => (
  <img
    src="/lumyn star logo.svg"
    alt="Lumyn"
    className="w-24 h-auto"
  />
)

// Base navigation items (will be enhanced with dynamic data)
const baseNavigationItems: Omit<NavItem, 'badge'>[] = [
  {
    id: 'dashboard',
    label: 'Overview',
    icon: OverviewIcon,
    href: '/',
  },
  {
    id: 'campaigns',
    label: 'Campaigns',
    icon: CampaignsIcon,
    href: '/campaigns',
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: AnalyticsIcon,
    href: '/analytics',
  },
  {
    id: 'calendar',
    label: 'Schedule',
    icon: CalendarIcon,
    href: '/calendar',
  },
  {
    id: 'brand-profile',
    label: 'Brand Profile',
    icon: BrandProfileIcon,
    href: '/brand-profile',
  },
]

const Sidebar: React.FC<SidebarProps> = ({ className, onAddBrand }) => {
  const { brands, fetchAllBrands, activeBrand, isInitialized, switchBrand } = useBrandStore()
  const { user } = useAuthStore()
  const { campaigns, fetchCampaigns } = useCampaignStore()
  const [showBrandDropdown, setShowBrandDropdown] = useState(false)
  const location = useLocation()

  // Fetch all brands when component mounts
  useEffect(() => {
    if (user) {
      fetchAllBrands()
    }
  }, [user, fetchAllBrands])

  // Fetch campaigns for sidebar count (will be updated when brand switches)
  useEffect(() => {
    if (activeBrand && isInitialized) {
      // Fetch campaigns for the active brand using the improved fetchCampaigns
      console.log('Sidebar: Fetching campaigns for active brand:', activeBrand.name);
      fetchCampaigns(activeBrand._id);

      // Also refresh dashboard stats for the active brand
      const refreshDashboardStats = async () => {
        try {
          const campaignStore = await import('@/stores/campaignStore');
          const { fetchDashboardStatsByBrand } = campaignStore.useCampaignStore.getState();
          await fetchDashboardStatsByBrand(activeBrand._id);
        } catch (error) {
          console.error('Error refreshing dashboard stats:', error);
        }
      };

      refreshDashboardStats();
    }
  }, [activeBrand, isInitialized, fetchCampaigns])
  
  // Create dynamic navigation items with live data
  const navigationItems: NavItem[] = baseNavigationItems.map(item => {
    if (item.id === 'campaigns') {
      return {
        ...item,
        badge: campaigns.length > 0 ? campaigns.length.toString() : undefined
      }
    }
    return item
  })

  // Helper function to determine if a menu item should be active
  const isMenuItemActive = (href: string) => {
    // Exact match
    if (location.pathname === href) {
      return true
    }

    // For non-root paths, check if current path starts with the menu item path
    // This handles child routes like /campaigns/4 highlighting the /campaigns menu
    if (href !== '/' && location.pathname.startsWith(href + '/')) {
      return true
    }

    return false
  }
  
  return (
    <div
      className={cn(
        'fixed left-0 top-0 h-full w-64 bg-dark-primary flex flex-col z-50',
        className
      )}
    >
      {/* Logo Section */}
      <div className="p-6">
        <div className="text-text-primary">
          <LumynLogo />
        </div>
      </div>

      {/* Brand Selector */}
      <div className="px-4 py-4">
        <div className="mb-3">
          <span className="text-xs font-semibold text-text-quaternary uppercase tracking-wider">WORKSPACE</span>
        </div>
        <div className="relative">
          <button
            onClick={() => setShowBrandDropdown(!showBrandDropdown)}
            className="w-full p-3 brand-selector-btn flex items-center justify-between"
          >
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-button rounded-lg flex items-center justify-center">
                <Building2 className="w-4 h-4 text-white" />
              </div>
              <div className="text-left">
                <div className="font-medium text-text-primary text-sm truncate max-w-[140px]">
                  {activeBrand?.name || 'Select Brand'}
                </div>
                <div className="text-text-quaternary text-xs truncate max-w-[140px]">
                  {activeBrand?.website ? new URL(activeBrand.website).hostname : activeBrand?.industry || 'No brand selected'}
                </div>
              </div>
            </div>
            <ChevronDown className={`w-4 h-4 text-text-quaternary transition-transform duration-200 ${showBrandDropdown ? 'rotate-180' : ''}`} />
          </button>

          {/* Brand Dropdown with Sticky Add Brand Button */}
          {showBrandDropdown && (
            <div className="absolute top-full left-0 right-0 mt-2 bg-dark-secondary border border-dark-quaternary rounded-lg shadow-dark-lg z-50 flex flex-col">
              {/* Scrollable Brand List */}
              <div className="max-h-64 overflow-y-auto rounded-t-lg">
                {brands.map((brand) => (
                  <button
                    key={brand._id}
                    onClick={async () => {
                      await switchBrand(brand)
                      setShowBrandDropdown(false)
                    }}
                    className={cn(
                      "w-full p-3 text-left hover:bg-dark-tertiary transition-colors duration-200 first:rounded-t-lg",
                      activeBrand?._id === brand._id ? 'bg-dark-tertiary' : ''
                    )}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-button rounded-lg flex items-center justify-center">
                        <Building2 className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <div className="font-medium text-text-primary text-sm">{brand.name}</div>
                        <div className="text-text-quaternary text-xs">
                          {brand.website ? new URL(brand.website).hostname : brand.industry}
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>

              {/* Sticky Add Brand Button - Always visible */}
              <div className="sticky bottom-0 border-t border-dark-quaternary bg-dark-secondary rounded-b-lg">
                <button
                  onClick={() => {
                    onAddBrand()
                    setShowBrandDropdown(false)
                  }}
                  className="w-full p-3 text-left hover:bg-dark-tertiary transition-colors duration-200 rounded-b-lg"
                  style={{
                    background: 'linear-gradient(to bottom, #323232 0%, #2a2a2a 100%)',
                    boxShadow: `
                      0 -2px 6px rgba(0, 0, 0, 0.2),
                      inset 0 1px 0 rgba(255, 255, 255, 0.05)
                    `
                  }}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-purple rounded-lg flex items-center justify-center">
                      <Plus className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <div className="font-medium text-text-primary text-sm">Add New Brand</div>
                      <div className="text-text-quaternary text-xs">Create a new workspace</div>
                    </div>
                  </div>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Navigation */}
      <nav className="flex-1 px-4 py-4">
        <div className="mb-3">
          <span className="text-[10px] font-semibold text-text-quaternary uppercase tracking-wider">MAIN MENU</span>
        </div>
        <div className="space-y-2">
          {navigationItems.map((item) => {
            const isActive = isMenuItemActive(item.href)
            const IconComponent = item.icon
            
            return (
              <Link
                key={item.id}
                to={item.href}
                className={cn(
                  'relative flex items-center justify-between px-3 py-2 rounded-xl transition-all duration-200 group',
                  isActive ? 'sidebar-item-active text-text-primary' : 'text-text-tertiary hover:text-text-primary hover:bg-dark-tertiary'
                )}
              >
                <div className="flex items-center space-x-3">
                  <IconComponent 
                    isActive={isActive} 
                    className="w-4 h-4 flex-shrink-0"
                  />
                  <span className="font-medium text-xs">{item.label}</span>
                </div>
                {item.badge && (
                  <span className={cn(
                    'px-2 py-0.5 text-[10px] font-medium rounded-full',
                    isActive
                      ? 'bg-brand-500 text-white'
                      : 'bg-dark-quaternary text-text-tertiary'
                  )}>
                    {item.badge}
                  </span>
                )}
              </Link>
            )
          })}
        </div>
      </nav>

             {/* Bottom Section */}
      <div className="px-4 py-4 relative">
        {/* Separator line with proper positioning */}
        <div className="absolute top-0 left-6 right-6 h-px bg-dark-quaternary/40"></div>
        <div className="space-y-2">
          {[
            { href: '/inbox', icon: InboxIcon, label: 'Review Center', badge: '12', badgeColor: 'bg-error-500' },
            { href: '/help', icon: QuestionCircleIcon, label: 'Help & Support' },
            { href: '/settings', icon: SettingsIcon, label: 'Settings' },
          ].map(item => {
            const isActive = location.pathname === item.href
            const IconComp = item.icon
            return (
              <Link
                key={item.href}
                to={item.href}
                className={cn(
                  'relative flex items-center justify-between px-3 py-2 rounded-xl transition-all duration-200 group',
                  isActive ? 'sidebar-item-active text-text-primary' : 'text-text-tertiary hover:text-text-primary hover:bg-dark-tertiary'
                )}
              >
                {/* Purple vertical bar */}
                {isActive && (
                  <span className="sidebar-vertical-bar absolute -left-1 top-1/2 -translate-y-1/2 w-1 h-6" />
                )}
                <div className="flex items-center space-x-3">
                  <IconComp className="w-4 h-4" />
                  <span className="font-medium text-xs">{item.label}</span>
                </div>
                {item.badge && (
                  <span className={`px-2 py-0.5 text-[10px] font-medium rounded-full text-white ${item.badgeColor}`}>{item.badge}</span>
                )}
              </Link>
            )
          })}
        </div>
      </div>
    </div>
  )
}

Sidebar.displayName = 'Sidebar'

export default Sidebar 