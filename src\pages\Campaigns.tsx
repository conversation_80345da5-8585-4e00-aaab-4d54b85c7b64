import { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Search, Grid, List, MoreVertical, ChevronUp, ChevronDown, Calendar, Target, TrendingUp, Users, Eye, FileText, Share2, Globe } from 'lucide-react';
import { channelMeta } from '@/icons/ChannelIcons';
import { cn } from '@/utils/cn';
import Button from '@/components/ui/Button';
import { Status } from '@/components/ui';
import type { CampaignStatus } from '@/components/ui';
import CampaignCreationWizard from '@/components/modals/CampaignCreationWizard';
import { useCampaignStore, type Campaign } from '@/stores/campaignStore';
import { useAuthStore } from '@/stores/authStore';
import { useBrandStore } from '@/stores/brandStore';

type SortField = 'name' | 'status' | 'posts' | 'startDate' | 'endDate';
type SortDirection = 'asc' | 'desc';


const typeConfig = {
  email: { Icon: Target, color: 'text-white', bg: 'bg-gradient-campaign-blue' },
  social: { Icon: Users, color: 'text-white', bg: 'bg-gradient-campaign-purple' },
  display: { Icon: Eye, color: 'text-white', bg: 'bg-gradient-campaign-orange' },
  search: { Icon: TrendingUp, color: 'text-white', bg: 'bg-gradient-campaign-green' }
};

const statusOptions = [
  { value: 'all', label: 'All Status' },
  { value: 'active', label: 'Active' },
  { value: 'draft', label: 'Draft' },
  { value: 'paused', label: 'Paused' },
  { value: 'completed', label: 'Completed' }
];

const typeOptions = [
  { value: 'all', label: 'All Types' },
  { value: 'email', label: 'Email' },
  { value: 'social', label: 'Social' },
  { value: 'display', label: 'Display' },
  { value: 'search', label: 'Search' }
];

export default function Campaigns() {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { activeBrand, isInitialized, brandSwitchTimestamp } = useBrandStore();
  const { campaigns, fetchCampaigns, isLoading } = useCampaignStore();
  const [isCreatingCampaign, setIsCreatingCampaign] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusOpen, setStatusOpen] = useState(false);
  const [typeOpen, setTypeOpen] = useState(false);
  const [sortField, setSortField] = useState<SortField>('startDate');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Fetch campaigns when component mounts or active brand changes
  useEffect(() => {
    console.log('🔄 Campaigns useEffect triggered:', {
      activeBrand: activeBrand?.name,
      brandId: activeBrand?._id,
      isInitialized,
      brandSwitchTimestamp,
      currentCampaignsCount: campaigns.length
    });

    // Wait for brand store to be initialized before proceeding
    if (!isInitialized) {
      console.log('📊 Campaigns: Waiting for brand store initialization...');
      return;
    }

    console.log('📊 Campaigns: Fetching campaigns for brand:', activeBrand?.name || 'all brands');
    if (activeBrand) {
      console.log('📊 Campaigns: Calling fetchCampaigns with brandId:', activeBrand._id);
      fetchCampaigns(activeBrand._id);
    } else {
      console.log('📊 Campaigns: Calling fetchCampaigns without brandId');
      fetchCampaigns();
    }
  }, [activeBrand, isInitialized, brandSwitchTimestamp, fetchCampaigns]);

  // Also fetch when user becomes available (in case of delayed auth)
  useEffect(() => {
    if (user && isInitialized && campaigns.length === 0) {
      console.log('User available but no campaigns, fetching...');
      if (activeBrand) {
        fetchCampaigns(activeBrand._id);
      } else {
        fetchCampaigns();
      }
    }
  }, [user, isInitialized, fetchCampaigns]);

  // Retry logic with limit to prevent infinite loops
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;

  useEffect(() => {
    if (campaigns.length === 0 && !isLoading && retryCount < maxRetries && isInitialized) {
      const timer = setTimeout(() => {
        console.log(`No campaigns after 2 seconds, retrying... (${retryCount + 1}/${maxRetries})`);
        setRetryCount(prev => prev + 1);
        if (activeBrand) {
          fetchCampaigns(activeBrand._id);
        } else {
          fetchCampaigns();
        }
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [campaigns.length, isLoading, retryCount, isInitialized, fetchCampaigns]);

  // Reset retry count when campaigns are loaded successfully or when brand changes
  useEffect(() => {
    if (campaigns.length > 0) {
      setRetryCount(0);
    }
  }, [campaigns.length]);

  // Reset retry count when active brand changes
  useEffect(() => {
    setRetryCount(0);
  }, [activeBrand]);

  // Debug: Log campaigns state changes
  useEffect(() => {
    console.log('Campaigns state changed:', campaigns.length, campaigns);
  }, [campaigns]);

  const filteredCampaigns = useMemo(() => {
    let filtered = campaigns.filter(campaign => {
      const matchesSearch = campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (campaign.purpose && campaign.purpose.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesStatus = statusFilter === 'all' || campaign.status === statusFilter;
      // For now, we'll skip type filtering since our real campaigns don't have the 'type' field yet
      // const matchesType = typeFilter === 'all' || campaign.type === typeFilter;

      return matchesSearch && matchesStatus;
    });

    // Sort the filtered results
    filtered.sort((a, b) => {
      let aVal: any = a[sortField];
      let bVal: any = b[sortField];

      if (sortField === 'startDate' || sortField === 'endDate') {
        aVal = new Date(aVal).getTime();
        bVal = new Date(bVal).getTime();
      }

      if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1;
      if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [searchTerm, statusFilter, typeFilter, sortField, sortDirection]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const handleCreateCampaign = () => {
    setIsCreatingCampaign(true);
  };

  const handleWizardBack = () => {
    setIsCreatingCampaign(false);
  };

  const handleWizardNext = () => {
    // TODO: Implement next step or completion
    console.log('Campaign creation next step');
  };

  const handleCampaignClick = (campaignId: string) => {
    navigate(`/campaigns/${campaignId}`);
  };

  // Channel meta function (matching StrategyStep)
  const getChannelMeta = (channel: string) => {
    const key = channel.toLowerCase()
    const defaults = { Icon: Globe, gradient: 'linear-gradient(180deg,#6366f1 0%,#4f46e5 100%)' }
    const gradients: Record<string, string> = {
      linkedin: 'linear-gradient(180deg,#0A66C2 0%,#004182 100%)',
      twitter: 'linear-gradient(180deg,#1DA1F2 0%,#0D8BDE 100%)',
      instagram: 'linear-gradient(180deg,#F58529 0%,#DD2A7B 100%)',
      facebook: 'linear-gradient(180deg,#1877F2 0%,#0e4ead 100%)',
      youtube: 'linear-gradient(180deg,#FF0000 0%,#BB0000 100%)',
      medium: 'linear-gradient(180deg,#444444 0%,#000000 100%)',
      blog: 'linear-gradient(180deg,#34d399 0%,#059669 100%)',
      email: 'linear-gradient(180deg,#6366f1 0%,#4f46e5 100%)',
    }
    const base = channelMeta[key]
    return base
      ? { Icon: base.Icon, gradient: gradients[key] || defaults.gradient }
      : defaults
  };

  // If creating campaign, show wizard
  if (isCreatingCampaign) {
    return (
      <CampaignCreationWizard
        onBack={handleWizardBack}
        onNext={handleWizardNext}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header - Like Overview Screen */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-xl font-semibold text-text-primary mb-1">Campaigns</h1>
          <p className="text-sm text-text-tertiary">Manage and track your marketing campaigns</p>
        </div>
        <Button
          variant="gradient"
          size="sm"
          icon={Plus}
          onClick={handleCreateCampaign}
        >
          Create Campaign
        </Button>
      </div>

      {/* Menu Bar */}
      <div className="card px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-tertiary w-4 h-4" />
              <input
                type="text"
                placeholder="Search campaigns..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input pl-10 pr-4 py-2 w-64"
              />
            </div>

            {/* Status Filter */}
            <div className="relative">
              <button
                type="button"
                onClick={() => setStatusOpen(!statusOpen)}
                className={cn(
                  'brand-selector-btn flex items-center justify-between px-3 py-2 text-sm min-w-[120px]',
                  (statusOpen || statusFilter !== 'all') && 'brand-selector-selected'
                )}
              >
                <span className={cn(
                  statusFilter === 'all' && !statusOpen ? 'text-text-secondary' : 'text-white'
                )}>
                  {statusOptions.find(opt => opt.value === statusFilter)?.label}
                </span>
                <ChevronDown className={cn(
                  'w-4 h-4 transition-transform ml-2',
                  statusOpen || statusFilter !== 'all' ? 'text-white' : 'text-text-tertiary',
                  statusOpen && 'rotate-180'
                )} />
              </button>
              {statusOpen && (
                <div className="dropdown-panel absolute top-full left-0 right-0 mt-1 z-20">
                  <div className="p-2">
                    {statusOptions.map(option => (
                      <button
                        key={option.value}
                        type="button"
                        onClick={() => {
                          setStatusFilter(option.value)
                          setStatusOpen(false)
                        }}
                        className={cn(
                          "dropdown-item",
                          statusFilter === option.value && "dropdown-item-active"
                        )}
                      >
                        {option.label}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Type Filter */}
            <div className="relative">
              <button
                type="button"
                onClick={() => setTypeOpen(!typeOpen)}
                className={cn(
                  'brand-selector-btn flex items-center justify-between px-3 py-2 text-sm min-w-[120px]',
                  (typeOpen || typeFilter !== 'all') && 'brand-selector-selected'
                )}
              >
                <span className={cn(
                  typeFilter === 'all' && !typeOpen ? 'text-text-secondary' : 'text-white'
                )}>
                  {typeOptions.find(opt => opt.value === typeFilter)?.label}
                </span>
                <ChevronDown className={cn(
                  'w-4 h-4 transition-transform ml-2',
                  typeOpen || typeFilter !== 'all' ? 'text-white' : 'text-text-tertiary',
                  typeOpen && 'rotate-180'
                )} />
              </button>
              {typeOpen && (
                <div className="dropdown-panel absolute top-full left-0 right-0 mt-1 z-20">
                  <div className="p-2">
                    {typeOptions.map(option => (
                      <button
                        key={option.value}
                        type="button"
                        onClick={() => {
                          setTypeFilter(option.value)
                          setTypeOpen(false)
                        }}
                        className={cn(
                          "dropdown-item",
                          typeFilter === option.value && "dropdown-item-active"
                        )}
                      >
                        {option.label}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* View Toggle - Icons Only */}
          <div className="view-toggle">
            <button
              onClick={() => setViewMode('grid')}
              className={cn(
                'p-2 rounded-md transition-all duration-150',
                viewMode === 'grid' ? 'view-toggle-btn-active' : 'text-text-tertiary hover:text-text-primary'
              )}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={cn(
                'p-2 rounded-md transition-all duration-150',
                viewMode === 'list' ? 'view-toggle-btn-active' : 'text-text-tertiary hover:text-text-primary'
              )}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div>
        {filteredCampaigns.length === 0 ? (
          <div className="text-center py-12">
            <Target className="w-12 h-12 text-text-tertiary mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-text-primary mb-2">No campaigns found</h3>
            <p className="text-sm text-text-secondary mb-6">Try adjusting your search or filters, or create a new campaign.</p>
            <Button
              variant="gradient"
              size="sm"
              icon={Plus}
              onClick={handleCreateCampaign}
            >
              Create Your First Campaign
            </Button>
          </div>
        ) : viewMode === 'grid' ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredCampaigns.map((campaign) => {
              // Use a default type since our real campaigns don't have the 'type' field yet
              const campaignType = 'social'; // Default to social for now
              const { Icon } = typeConfig[campaignType];

              return (
                <div
                  key={campaign._id}
                  onClick={() => handleCampaignClick(campaign._id)}
                  className="campaign-card hover:bg-dark-tertiary transition-all cursor-pointer flex flex-col h-full space-y-4"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start gap-3">
                      <div className={`icon-button ${typeConfig[campaignType].bg}`}>
                        <Icon className="w-5 h-5 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-text-primary text-sm mb-1">{campaign.name}</h3>
                        <div className="flex items-center gap-2 text-xs text-text-tertiary mb-2">
                          <Calendar className="w-3 h-3" />
                          <span>{formatDate(campaign.startDate)} - {formatDate(campaign.endDate)}</span>
                          <Status status={campaign.status} type="campaign" />
                        </div>
                      </div>
                    </div>

                    <div className="relative">
                      <button
                        onClick={(e) => e.stopPropagation()}
                        className="p-2 rounded-lg hover:bg-dark-quaternary transition-colors"
                      >
                        <MoreVertical className="w-4 h-4 text-text-tertiary" />
                      </button>
                    </div>
                  </div>

                  <p className="text-xs text-text-secondary mb-4 line-clamp-2">{campaign.purpose || 'No description available'}</p>

                  <div className="grid grid-cols-2 gap-4 mb-4 mt-auto">
                    <div>
                      <div className="text-xs text-text-tertiary mb-2">Posts</div>
                      <div className="text-sm font-medium text-text-primary">{campaign.contentCount || 0}</div>
                    </div>
                    <div>
                      <div className="text-xs text-text-tertiary mb-2">Channels</div>
                      <div className="flex items-center gap-1 flex-wrap">
                        {campaign.targetChannels && campaign.targetChannels.length > 0 ? (
                          [...new Set(campaign.targetChannels)].map((ch) => {
                            const meta = channelMeta[ch.toLowerCase()];
                            if (!meta) return null;
                            const { Icon, color, bg } = meta;
                            return (
                              <span key={ch} className={`w-6 h-6 ${bg} ${color} rounded-full flex items-center justify-center`}>
                                <Icon className="w-3 h-3" />
                              </span>
                            );
                          })
                        ) : (
                          <span className="text-xs text-text-tertiary">No channels</span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="separator-line my-4" />
                  <Button
                    variant="secondary"
                    size="sm"
                    className="btn-secondary rounded-full w-full justify-center"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCampaignClick(campaign._id);
                    }}
                  >
                    View Campaign
                  </Button>

                </div>
              );
            })}
          </div>
        ) : (
          <div className="space-y-4">
            <div className="card overflow-hidden p-2 md:p-3">
              {/* Professional Table Structure */}
              <table className="w-full table-fixed">
                <thead className="border-b border-dark-quaternary">
                  <tr>
                    <th className="px-6 py-4 text-left" style={{ width: '280px' }}>
                      <button
                        onClick={() => handleSort('name')}
                        className="flex items-center gap-1 text-xs font-medium text-text-secondary uppercase tracking-wider hover:text-text-primary transition-colors"
                      >
                        Campaign Name
                        {sortField === 'name' && (
                          sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                        )}
                      </button>
                    </th>
                    <th className="px-6 py-4 text-left" style={{ width: '140px' }}>
                      <span className="flex items-center gap-1 text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Channels
                      </span>
                    </th>
                    <th className="px-6 py-4 text-left" style={{ width: '110px' }}>
                      <button
                        onClick={() => handleSort('status')}
                        className="flex items-center gap-1 text-xs font-medium text-text-secondary uppercase tracking-wider hover:text-text-primary transition-colors"
                      >
                        Status
                        {sortField === 'status' && (
                          sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                        )}
                      </button>
                    </th>
                    <th className="px-6 py-4 text-right" style={{ width: '100px' }}>
                      <button
                        onClick={() => handleSort('posts')}
                        className="flex items-center gap-1 text-xs font-medium text-text-secondary uppercase tracking-wider hover:text-text-primary transition-colors ml-auto"
                      >
                        Posts
                        {sortField === 'posts' && (
                          sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                        )}
                      </button>
                    </th>
                    <th className="px-6 py-4 text-right" style={{ width: '130px' }}>
                      <button
                        onClick={() => handleSort('startDate')}
                        className="flex items-center gap-1 text-xs font-medium text-text-secondary uppercase tracking-wider hover:text-text-primary transition-colors ml-auto"
                      >
                        Start Date
                        {sortField === 'startDate' && (
                          sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                        )}
                      </button>
                    </th>
                    <th className="px-6 py-4 text-right" style={{ width: '130px' }}>
                      <button
                        onClick={() => handleSort('endDate')}
                        className="flex items-center gap-1 text-xs font-medium text-text-secondary uppercase tracking-wider hover:text-text-primary transition-colors ml-auto"
                      >
                        End Date
                        {sortField === 'endDate' && (
                          sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                        )}
                      </button>
                    </th>
                    <th className="px-6 py-4 text-center" style={{ width: '150px' }}>
                      <span className="text-xs font-medium text-text-secondary uppercase tracking-wider">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-dark-quaternary">
                  {(() => {
                    // Pagination logic
                    const totalItems = filteredCampaigns.length
                    const totalPages = Math.ceil(totalItems / itemsPerPage)
                    const startIndex = (currentPage - 1) * itemsPerPage
                    const endIndex = startIndex + itemsPerPage
                    const paginatedCampaigns = filteredCampaigns.slice(startIndex, endIndex)

                    return paginatedCampaigns.map((campaign) => {
                      // Use a default type since our real campaigns don't have the 'type' field yet
                      const campaignType = 'social'; // Default to social for now
                      const { Icon } = typeConfig[campaignType];

                      return (
                        <tr key={campaign._id} onClick={() => handleCampaignClick(campaign._id)} className="hover:bg-dark-tertiary transition-colors cursor-pointer">
                          <td className="px-6 py-4">
                            <div className="flex items-center gap-3">
                              <div className={`campaign-list-icon ${typeConfig[campaignType].bg}`}>
                                <Icon className={`w-4 h-4 ${typeConfig[campaignType].color}`} />
                              </div>
                              <div className="min-w-0 flex-1">
                                <div className="font-medium text-text-primary text-sm truncate">{campaign.name}</div>
                                <div className="text-xs font-normal text-text-tertiary mt-0.5 truncate">{campaign.purpose || 'No description available'}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex items-center gap-1 flex-wrap">
                              {campaign.targetChannels && campaign.targetChannels.length > 0 ? (
                                [...new Set(campaign.targetChannels)].map((ch) => {
                                  const meta = getChannelMeta(ch);
                                  return (
                                    <span
                                      key={ch}
                                      className="w-5 h-5 rounded-full flex items-center justify-center"
                                      style={{ background: meta.gradient }}
                                    >
                                      <meta.Icon className="w-3 h-3 text-white" />
                                    </span>
                                  );
                                })
                              ) : (
                                <span className="text-xs text-text-tertiary">No channels</span>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <Status status={campaign.status} type="campaign" />
                          </td>
                          <td className="px-6 py-4 text-right">
                            <span className="text-sm font-medium text-text-primary">{campaign.contentCount || 0}</span>
                          </td>
                          <td className="px-6 py-4 text-right">
                            <span className="text-xs font-normal text-text-tertiary">{formatDate(campaign.startDate)}</span>
                          </td>
                          <td className="px-6 py-4 text-right">
                            <span className="text-xs font-normal text-text-tertiary">{formatDate(campaign.endDate)}</span>
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex items-center justify-center gap-1">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleCampaignClick(campaign._id);
                                }}
                                className="inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-text-tertiary border border-transparent hover:text-success-500 hover:border-success-500 hover:bg-success-500/10 rounded-md transition-colors"
                              >
                                <Eye className="w-3 h-3" />
                                View
                              </button>
                              <button
                                onClick={(e) => e.stopPropagation()}
                                className="p-1.5 rounded-md hover:bg-dark-quaternary transition-colors"
                              >
                                <MoreVertical className="w-4 h-4 text-text-tertiary" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      );
                    })
                  })()}
                </tbody>
              </table>
            </div>

            {/* Pagination - Always show for testing */}
            {(() => {
              const totalItems = filteredCampaigns.length
              const totalPages = Math.ceil(totalItems / itemsPerPage)
              const startIndex = (currentPage - 1) * itemsPerPage
              const endIndex = startIndex + itemsPerPage

              return (
                <div className="flex items-center justify-between px-4 py-3 bg-dark-secondary border border-dark-quaternary rounded-lg">
                  <div className="flex items-center gap-2 text-sm text-text-tertiary">
                    <span>Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} results</span>
                    <span className="text-xs text-text-quaternary ml-2">
                      (Page {currentPage} of {Math.max(1, totalPages)})
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="px-3 py-1.5 text-sm font-medium text-text-secondary bg-dark-tertiary border border-dark-quaternary rounded-md hover:bg-dark-quaternary disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      Previous
                    </button>
                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.max(1, totalPages) }, (_, i) => i + 1).map((page) => (
                        <button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={cn(
                            "px-3 py-1.5 text-sm font-medium rounded-md transition-colors",
                            currentPage === page
                              ? "bg-brand-500 text-white"
                              : "text-text-secondary bg-dark-tertiary border border-dark-quaternary hover:bg-dark-quaternary"
                          )}
                        >
                          {page}
                        </button>
                      ))}
                    </div>
                    <button
                      onClick={() => setCurrentPage(Math.min(Math.max(1, totalPages), currentPage + 1))}
                      disabled={currentPage === totalPages || totalPages <= 1}
                      className="px-3 py-1.5 text-sm font-medium text-text-secondary bg-dark-tertiary border border-dark-quaternary rounded-md hover:bg-dark-quaternary disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )
            })()}
          </div>
        )}
      </div>
    </div>
  );
} 