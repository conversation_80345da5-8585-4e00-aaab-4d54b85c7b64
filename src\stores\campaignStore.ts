import { create } from 'zustand';
import { api } from './authStore';

// Types for campaign data
interface Campaign {
  _id: string;
  userId: string;
  brandId: string;
  name: string;
  purpose: string;
  callToAction: string;
  instructions?: string;
  startDate: string;
  endDate: string;
  targetChannels: string[];
  numberOfPosts: number;
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  progress: number;
  researchInsights?: {
    industryTrends: Array<{
      trend: string;
      description: string;
    }>;
    competitiveAnalysis: Array<{
      title: string;
      description: string;
    }>;
    audienceBehavior: Array<{
      behavior: string;
      description: string;
    }>;
  };
  campaignStrategy?: {
    campaignThemes: Array<{
      theme: string;
      description: string;
      tags: string[];
    }>;
    channelSpecificStrategies: Array<{
      channel: string;
      approach: string;
      contentTypes: string[];
      contentFrequency: string;
      targetAudience: string;
    }>;
  };
  contentCalendar?: Array<{
    date: string;
    channel: string;
    contentType: string;
    topic: string;
    hashtags: string[];
    status: 'planned' | 'created' | 'approved' | 'published';
  }>;
  generatedContent?: Array<{
    date: string;
    channel: string;
    contentType: string;
    topic: string;
    hashtags: string[];
    status: 'generated' | 'approved' | 'published';
  }>;
  campaignFiles?: string[];
  selectedBrandFiles?: string[];
  isAIGenerated: boolean;
  contentCount: number;
  dueDate: string;
  createdAt: string;
  updatedAt: string;
  brand?: {
    _id: string;
    name: string;
    industry: string;
  };
}

interface CreateCampaignData {
  name: string;
  brandId: string;
  purpose: string;
  callToAction: string;
  startDate: string;
  endDate: string;
  targetChannels: string[];
  instructions?: string;
  numberOfPosts?: number;
  researchInsights?: any;
  campaignStrategy?: any;
  contentCalendar?: any[];
  isAIGenerated?: boolean;
}

interface DashboardStats {
  totalCampaigns: number;
  activeCampaigns: number;
  completedCampaigns: number;
  totalPosts: number;
  engagementRate: string;
  totalReach: string;
  brandName?: string;
  // Growth percentages
  campaignsGrowth?: string;
  postsGrowth?: string;
  engagementGrowth?: string;
  reachGrowth?: string;
}

interface CampaignState {
  // State
  campaigns: Campaign[];
  currentCampaign: Campaign | null;
  dashboardStats: DashboardStats | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchCampaigns: (brandId?: string, retryCount?: number) => Promise<void>;
  fetchCampaignsByBrand: (brandId: string) => Promise<void>;
  fetchCampaign: (campaignId: string) => Promise<void>;
  createCampaign: (campaignData: CreateCampaignData) => Promise<Campaign>;
  updateCampaign: (campaignId: string, campaignData: Partial<Campaign>) => Promise<void>;
  deleteCampaign: (campaignId: string) => Promise<void>;
  fetchDashboardStats: (brandId?: string) => Promise<void>;
  fetchDashboardStatsByBrand: (brandId: string) => Promise<void>;
  clearError: () => void;
  setCampaigns: (campaigns: Campaign[]) => void;
  clearCampaigns: () => void;
}

export const useCampaignStore = create<CampaignState>((set, get) => ({
  // Initial state
  campaigns: [],
  currentCampaign: null,
  dashboardStats: null,
  isLoading: false,
  error: null,

  // Fetch all campaigns for current user or specific brand
  fetchCampaigns: async (brandId?: string, retryCount = 0) => {
    console.log('🔄 fetchCampaigns called, brandId:', brandId, 'retry:', retryCount);
    set({ isLoading: true, error: null });

    try {
      let response;
      if (brandId) {
        console.log('📡 Making API call to /campaigns/brand/' + brandId);
        response = await api.get(`/campaigns/brand/${brandId}`);
      } else {
        console.log('📡 Making API call to /campaigns (all campaigns)');
        response = await api.get('/campaigns');
      }

      const { campaigns } = response.data.data;

      console.log('✅ API response received:', campaigns?.length || 0, 'campaigns');

      set({
        campaigns: campaigns || [],
        isLoading: false,
        error: null
      });

      console.log('💾 Campaigns stored in state');
    } catch (error: any) {
      console.error('❌ fetchCampaigns error:', error);

      // If it's an auth error and we haven't retried yet, wait and retry
      if (error.response?.status === 401 && retryCount < 2) {
        console.log('🔄 Auth error, retrying in 1 second...');
        setTimeout(() => {
          get().fetchCampaigns(brandId, retryCount + 1);
        }, 1000);
        return;
      }

      const errorMessage = error.response?.data?.error?.message || 'Failed to fetch campaigns';
      set({
        campaigns: [],
        isLoading: false,
        error: errorMessage
      });
    }
  },

  // Fetch campaigns for a specific brand
  fetchCampaignsByBrand: async (brandId: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await api.get(`/campaigns/brand/${brandId}`);
      const { campaigns } = response.data.data;
      
      set({
        campaigns: campaigns || [],
        isLoading: false,
        error: null
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || 'Failed to fetch brand campaigns';
      set({
        campaigns: [],
        isLoading: false,
        error: errorMessage
      });
    }
  },

  // Fetch single campaign
  fetchCampaign: async (campaignId: string) => {
    set({ isLoading: true, error: null });

    try {
      const response = await api.get(`/campaigns/${campaignId}`);
      const { campaign } = response.data.data;

      set({
        currentCampaign: campaign,
        isLoading: false,
        error: null
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || 'Failed to fetch campaign';
      set({
        currentCampaign: null,
        isLoading: false,
        error: errorMessage
      });
    }
  },

  // Create new campaign
  createCampaign: async (campaignData: CreateCampaignData): Promise<Campaign> => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await api.post('/campaigns', campaignData);
      const { campaign } = response.data.data;
      
      // Add new campaign to the list
      const { campaigns } = get();
      set({
        campaigns: [campaign, ...campaigns],
        isLoading: false,
        error: null
      });
      
      console.log('✅ Campaign created successfully');
      return campaign;
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || 'Failed to create campaign';
      set({
        isLoading: false,
        error: errorMessage
      });
      throw new Error(errorMessage);
    }
  },

  // Update campaign
  updateCampaign: async (campaignId: string, campaignData: Partial<Campaign>) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await api.put(`/campaigns/${campaignId}`, campaignData);
      const { campaign: updatedCampaign } = response.data.data;
      
      // Update campaign in the list
      const { campaigns } = get();
      const updatedCampaigns = campaigns.map(campaign => 
        campaign._id === campaignId ? updatedCampaign : campaign
      );
      
      set({
        campaigns: updatedCampaigns,
        currentCampaign: updatedCampaign,
        isLoading: false,
        error: null
      });
      
      console.log('✅ Campaign updated successfully');
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || 'Failed to update campaign';
      set({
        isLoading: false,
        error: errorMessage
      });
      throw new Error(errorMessage);
    }
  },

  // Delete campaign
  deleteCampaign: async (campaignId: string) => {
    set({ isLoading: true, error: null });
    
    try {
      await api.delete(`/campaigns/${campaignId}`);
      
      // Remove campaign from the list
      const { campaigns } = get();
      const updatedCampaigns = campaigns.filter(campaign => campaign._id !== campaignId);
      
      set({
        campaigns: updatedCampaigns,
        isLoading: false,
        error: null
      });
      
      console.log('✅ Campaign deleted successfully');
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || 'Failed to delete campaign';
      set({
        isLoading: false,
        error: errorMessage
      });
      throw new Error(errorMessage);
    }
  },

  // Fetch dashboard statistics (all brands or specific brand)
  fetchDashboardStats: async (brandId?: string) => {
    set({ isLoading: true, error: null });

    try {
      let response;
      if (brandId) {
        response = await api.get(`/campaigns/stats/dashboard/brand/${brandId}`);
      } else {
        response = await api.get('/campaigns/stats/dashboard');
      }

      const stats = response.data.data;

      set({
        dashboardStats: stats,
        isLoading: false,
        error: null
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || 'Failed to fetch dashboard stats';
      set({
        dashboardStats: null,
        isLoading: false,
        error: errorMessage
      });
    }
  },

  // Fetch dashboard statistics for specific brand
  fetchDashboardStatsByBrand: async (brandId: string) => {
    set({ isLoading: true, error: null });

    try {
      const response = await api.get(`/campaigns/stats/dashboard/brand/${brandId}`);
      const stats = response.data.data;

      set({
        dashboardStats: stats,
        isLoading: false,
        error: null
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || 'Failed to fetch brand dashboard stats';
      set({
        dashboardStats: null,
        isLoading: false,
        error: errorMessage
      });
    }
  },

  // Clear error
  clearError: () => {
    set({ error: null });
  },

  // Set campaigns directly
  setCampaigns: (campaigns: Campaign[]) => {
    set({ campaigns });
  },

  // Clear campaigns (useful when switching brands)
  clearCampaigns: () => {
    set({ campaigns: [], currentCampaign: null });
  }
}));

// Export types for use in components
export type {
  Campaign,
  CreateCampaignData,
  DashboardStats
};